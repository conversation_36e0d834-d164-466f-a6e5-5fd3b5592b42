--cpu=Cortex-M4.fp.sp
"uart_maixcam\startup_stm32f429xx.o"
"uart_maixcam\main.o"
"uart_maixcam\gpio.o"
"uart_maixcam\usart.o"
"uart_maixcam\stm32f4xx_it.o"
"uart_maixcam\stm32f4xx_hal_msp.o"
"uart_maixcam\stm32f4xx_hal_uart.o"
"uart_maixcam\stm32f4xx_hal_rcc.o"
"uart_maixcam\stm32f4xx_hal_rcc_ex.o"
"uart_maixcam\stm32f4xx_hal_flash.o"
"uart_maixcam\stm32f4xx_hal_flash_ex.o"
"uart_maixcam\stm32f4xx_hal_flash_ramfunc.o"
"uart_maixcam\stm32f4xx_hal_gpio.o"
"uart_maixcam\stm32f4xx_hal_dma_ex.o"
"uart_maixcam\stm32f4xx_hal_dma.o"
"uart_maixcam\stm32f4xx_hal_pwr.o"
"uart_maixcam\stm32f4xx_hal_pwr_ex.o"
"uart_maixcam\stm32f4xx_hal_cortex.o"
"uart_maixcam\stm32f4xx_hal.o"
"uart_maixcam\stm32f4xx_hal_exti.o"
"uart_maixcam\system_stm32f4xx.o"
--strict --scatter "uart_maixcam\uart_maixcam.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "uart_maixcam.map" -o uart_maixcam\uart_maixcam.axf